<?php
declare(strict_types=1);

namespace app\api\controller\admin;

use app\api\service\ArticleCategoryService;
use app\api\annotation\RequireRole;
use think\Request;
use think\Response;
use think\exception\ValidateException;

/**
 * 文章分类管理控制器
 *
 * 使用方法级权限控制，不同操作需要不同权限
 */
class ArticleCategoryController
{
    protected ArticleCategoryService $categoryService;
    
    public function __construct()
    {
        $this->categoryService = new ArticleCategoryService();
    }
    
    /**
     * 获取分类列表
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '查看分类列表需要内容管理员或超级管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 15);
            
            // 构建查询条件
            $where = [];
            if ($request->has('name')) {
                $where['name'] = $request->param('name');
            }
            if ($request->has('type')) {
                $where['type'] = $request->param('type');
            }
            if ($request->has('parent_id')) {
                $where['parent_id'] = $request->param('parent_id');
            }
            if ($request->has('is_show')) {
                $where['is_show'] = $request->param('is_show');
            }
            if ($request->has('level')) {
                $where['level'] = $request->param('level');
            }
            
            $result = $this->categoryService->getList($page, $limit, $where);
            
            // 转换Bean为数组
            $list = [];
            foreach ($result['list'] as $category) {
                $list[] = $category->toArray();
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit'],
                    'pages' => $result['pages']
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取分类列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取分类详情
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '查看分类详情需要内容管理员或超级管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('分类ID不能为空');
            }
            
            $category = $this->categoryService->getById($id);
            if (!$category) {
                return json([
                    'code' => 404,
                    'message' => '分类不存在',
                    'data' => null
                ], 404);
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $category->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取分类详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建分类
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '创建分类需要内容管理员或超级管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'parent_id', 'name', 'slug', 'type', 'description',
                'link_url', 'cover_image', 'sort_order', 'is_show',
                'seo_title', 'seo_keywords', 'seo_description'
            ]);
            
            $category = $this->categoryService->create($data);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $category->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建分类失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新分类
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '更新分类需要内容管理员或超级管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('分类ID不能为空');
            }
            
            $data = $request->only([
                'parent_id', 'name', 'slug', 'type', 'description',
                'link_url', 'cover_image', 'sort_order', 'is_show',
                'seo_title', 'seo_keywords', 'seo_description'
            ]);
            
            $category = $this->categoryService->update($id, $data);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $category->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新分类失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除分类
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole('super_admin', '删除分类需要超级管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('分类ID不能为空');
            }
            
            $result = $this->categoryService->delete($id);
            
            return json([
                'code' => 200,
                'message' => $result ? '删除成功' : '删除失败',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除分类失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取菜单树
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '获取菜单树需要内容管理员或超级管理员权限')]
    public function tree(Request $request): Response
    {
        try {
            $onlyVisible = (bool)$request->param('only_visible', true);
            
            $tree = $this->categoryService->getMenuTree($onlyVisible);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $tree
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取菜单树失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取子分类
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '获取子分类需要内容管理员或超级管理员权限')]
    public function children(Request $request): Response
    {
        try {
            $parentId = (int)$request->param('parent_id', 0);
            $onlyVisible = (bool)$request->param('only_visible', false);

            $children = $this->categoryService->getChildren($parentId, $onlyVisible);

            // 转换Bean为数组
            $list = [];
            foreach ($children as $category) {
                $list[] = $category->toArray();
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $list
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取子分类失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取面包屑路径
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '获取面包屑路径需要内容管理员或超级管理员权限')]
    public function breadcrumb(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('分类ID不能为空');
            }

            $breadcrumb = $this->categoryService->getBreadcrumb($id);

            // 转换Bean为数组
            $list = [];
            foreach ($breadcrumb as $category) {
                $list[] = $category->toArray();
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $list
            ]);

        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取面包屑路径失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 移动分类
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '移动分类需要内容管理员或超级管理员权限')]
    public function move(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            $newParentId = (int)$request->param('new_parent_id', 0);

            if (!$id) {
                throw new ValidateException('分类ID不能为空');
            }

            $result = $this->categoryService->move($id, $newParentId);

            return json([
                'code' => 200,
                'message' => $result ? '移动成功' : '移动失败',
                'data' => null
            ]);

        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '移动分类失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量更新排序
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '批量更新排序需要内容管理员或超级管理员权限')]
    public function updateSort(Request $request): Response
    {
        try {
            $sortData = $request->param('sort_data', []);

            if (empty($sortData) || !is_array($sortData)) {
                throw new ValidateException('排序数据不能为空');
            }

            $result = $this->categoryService->updateSort($sortData);

            return json([
                'code' => 200,
                'message' => $result ? '排序更新成功' : '排序更新失败',
                'data' => null
            ]);

        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新排序失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 切换显示状态
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '切换显示状态需要内容管理员或超级管理员权限')]
    public function toggleShow(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');

            if (!$id) {
                throw new ValidateException('分类ID不能为空');
            }

            $result = $this->categoryService->toggleShow($id);

            return json([
                'code' => 200,
                'message' => $result ? '状态切换成功' : '状态切换失败',
                'data' => null
            ]);

        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '切换状态失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 根据类型获取分类
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(['content_admin', 'super_admin'], '根据类型获取分类需要内容管理员或超级管理员权限')]
    public function getByType(Request $request): Response
    {
        try {
            $type = $request->param('type');
            $onlyVisible = (bool)$request->param('only_visible', false);

            if (empty($type)) {
                throw new ValidateException('分类类型不能为空');
            }

            $categories = $this->categoryService->getByType($type, $onlyVisible);

            // 转换Bean为数组
            $list = [];
            foreach ($categories as $category) {
                $list[] = $category->toArray();
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $list
            ]);

        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取分类失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
