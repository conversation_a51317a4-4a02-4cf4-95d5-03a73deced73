<?php
declare(strict_types=1);

namespace app\api\controller;

use app\api\test\BeanTest;
use think\Response;

/**
 * 测试控制器
 */
class TestController
{
    /**
     * Bean测试
     */
    public function bean(): Response
    {
        ob_start();

        try {
            $test = new BeanTest();
            $test->runAllTests();
        } catch (\Exception $e) {
            echo "测试异常: " . $e->getMessage() . "\n";
            echo "异常文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
            echo "异常堆栈:\n" . $e->getTraceAsString() . "\n";
        }

        $output = ob_get_clean();

        return response($output, 200, ['Content-Type' => 'text/plain; charset=utf-8']);
    }

    /**
     * Service测试
     */
    public function service(): Response
    {
        ob_start();

        try {
            echo "=== 测试AdminAuthService ===\n";

            $authService = new \app\api\service\AdminAuthService();

            // 测试登录状态检查
            echo "当前登录状态: " . ($authService->isLogin() ? '已登录' : '未登录') . "\n";

            // 测试用户信息获取
            $user = $authService->getUser();
            if ($user) {
                echo "当前用户: " . $user['username'] . " (" . $user['real_name'] . ")\n";
                echo "用户角色: " . $user['role'] . "\n";
            }

            echo "\n=== 测试AdminUserService ===\n";

            $userService = new \app\api\service\AdminUserService();

            // 测试获取用户列表
            $result = $userService->getList(1, 5);
            echo "用户总数: " . $result['total'] . "\n";
            echo "当前页用户数: " . count($result['list']) . "\n";

            if (!empty($result['list'])) {
                echo "用户列表:\n";
                foreach ($result['list'] as $user) {
                    echo "  - ID: {$user->id}, 用户名: {$user->username}, 角色: {$user->getRoleText()}\n";
                }
            }

            // 测试根据ID获取用户
            $user = $userService->getById(1);
            if ($user) {
                echo "\n用户详情 (ID=1):\n";
                echo "  用户名: {$user->username}\n";
                echo "  邮箱: {$user->email}\n";
                echo "  真实姓名: {$user->realName}\n";
                echo "  角色: {$user->getRoleText()}\n";
                echo "  状态: {$user->getStatusText()}\n";
            }

        } catch (\Exception $e) {
            echo "Service测试异常: " . $e->getMessage() . "\n";
            echo "异常文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
        }

        $output = ob_get_clean();

        return response($output, 200, ['Content-Type' => 'text/plain; charset=utf-8']);
    }
}
